ARG AWS_ACCOUNT=************
ARG BASE_REPO=${AWS_ACCOUNT}.dkr.ecr.cn-northwest-1.amazonaws.com.cn
FROM ${BASE_REPO}/arthas:3.5.1-no-jdk as arthas3
FROM public.ecr.aws/amazoncorretto/amazoncorretto:11.0.24-al2

ARG BASE_REPO
ARG AWS_ACCOUNT
ARG BASE_ENV
ARG BASE_JDK
RUN echo "BASE_REPO IS ${BASE_REPO}"

RUN echo "BASE_ENV IS ${BASE_ENV}, BASE_JDK IS ${BASE_JDK} ,AWS_ACCOUNT IS ${AWS_ACCOUNT}"
COPY --from=arthas3 /opt/arthas /opt/arthas

RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone
# RUN bash -c 'touch /opt/loyalty-api-server.jar'


# RUN 用于容器内部执行命令
RUN mkdir -p /opt/app
WORKDIR /opt/app

#将项目放到/usr/local/project下
COPY ./target/app.jar /opt/app/app.jar


RUN mkdir -p /data
RUN yum install shadow-utils -y
RUN useradd -r -m -u 1001 appuser
RUN chmod -R 777 /data
RUN chmod -R 777 /opt
RUN chmod -R 777 /root
USER appuser

ENTRYPOINT ["java","-jar","/opt/app/app.jar"]
