apiVersion: v1
kind: ConfigMap
metadata:
  name: saml-sso-project-config
  labels:
    app: saml-sso-project-config
data:
  multipart_max_size: "1MB"
#  kafka_addr: b-1.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-2.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9092
#  kafka_v1_addr: b-1.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9094,b-2.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9094
#  redis_addr: redis-dev-ecp-1-001.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn
#  redis_addr_master: master.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn
#  redis_addr_slave: replica.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn
#  aurora_addr_write: rds-mysql-dev-ecp-1.cluster-cbv7bxi3fult.rds.cn-northwest-1.amazonaws.com.cn:3306
#  aurora_addr_reader: rds-mysql-dev-ecp-1.cluster-ro-cbv7bxi3fult.rds.cn-northwest-1.amazonaws.com.cn:3306
#  #TBD MODIFY
#  consumer_addr: http://crm-consumer-center-service:80
  #log_environment: dev
  #log_version: "1.0.0"
  #sql_show: "true"
  #log_print: "true"
 # key_store_location_v2: "file:/home/<USER>/consumerhub/consumer_hub.consumer_hub_CN.kaas.3stripes.net.jks"
 # trust_store_location_v2: "file:/home/<USER>/consumerhub/client.truststore.jks"
 # ssl_key_store_location_v1: "file:/home/<USER>/consumerhub/gca_fd_crm.crm.kaas.3stripes.net.jks"
 # trust_store_location_v1: "file:/home/<USER>/consumerhub/kafka.truststore.jks"
  s3_bucket_name_file: "s3-jlr-ecp-file-test"
  s3_bucket_name_image: "s3-jlr-ecp-image-test"
  s3_bucket_name_video: "s3-jlr-ecp-video-test"
  kafka_batch_consume_num: '2000'
 # V1_KAFKA_ENABLE: '0'
  time_query_range: '15'
  java_opts: "-Xms512m -Xmx2048m -XX:MaxGCPauseMillis=200"
  spring_documentation_enabled: "true"
