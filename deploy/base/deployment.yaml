apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: saml-sso-project
  name: saml-sso-project
spec:
  replicas: 1
  selector:
    matchLabels:
      app: saml-sso-project
  template:
    metadata:
      labels:
        app: saml-sso-project
    spec:
#      topologySpreadConstraints:
#        - maxSkew: 1
#          topologyKey: topology.kubernetes.io/zone
#          whenUnsatisfiable: ScheduleAnyway
#          labelSelector:
#            matchLabels:
#              app: saml-sso-project
#      affinity:
#        podAntiAffinity:
#          preferredDuringSchedulingIgnoredDuringExecution:
#            - weight: 100
#              podAffinityTerm:
#                labelSelector:
#                  matchExpressions:
#                    - key: app
#                      operator: In
#                      values:
#                        - saml-sso-project
#                topologyKey: kubernetes.io/hostname
      containers:
        - image: public.ecr.aws/nginx/nginx:stable-alpine
#          imagePullPolicy: IfNotPresent
          imagePullPolicy: Always
          name: saml-sso-project
          env:
            - name: MULTIPART_MAX_SIZE
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: multipart_max_size
            - name: REDIS_ADDR_MASTER
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: redis_addr_master
            - name: REDIS_ADDR_SLAVE
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: redis_addr_slave
            - name: AURORA_ADDR_WRITE
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: aurora_addr_write
            - name: AURORA_ADDR_READER
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: aurora_addr_reader
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: db_user
            - name: DB_PASSWD
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: db_passwd
            - name: REDIS_PASSWD
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: redis_passwd
            - name: KAFKA_V1_ADDR
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: kafka_v1_addr
            - name: KAFKA_ADDR
              valueFrom:
                secretKeyRef:
                  name: saml-sso-project-externalsecret
                  key: kafka_addr

            - name: S3_BUCKET_NAME_FILE
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: s3_bucket_name_file
            - name: S3_BUCKET_NAME_IMAGE
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: s3_bucket_name_image
            - name: S3_BUCKET_NAME_VIDEO
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: s3_bucket_name_video
            - name: S3_BUCKET_NAME_VIDEO
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: s3_bucket_name_video

            - name: KAFKA_BATCH_CONSUME_NUM
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: kafka_batch_consume_num

            - name: SPRING_DOCUMENTATION_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: saml-sso-project-config
                  key: spring_documentation_enabled


          ports:
            - containerPort: 8009
              name: http
#          volumeMounts:
#            - mountPath: /home/<USER>/consumerhub/client.truststore.jks
#              name: fdp-secret
#              subPath: client.truststore.jks
#      imagePullSecrets:
#        - name: docker-secret
      securityContext:
        fsGroup: 1000
      volumes:
        - name: fdp-secret
          secret:
            secretName: saml-sso-project-secret
            items:
              - key: client.truststore.jks
                path: client.truststore.jks
