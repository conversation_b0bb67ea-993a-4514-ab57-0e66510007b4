[INFO] Scanning for projects...
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/org/apache/logging/log4j/log4j-bom/2.17.0/log4j-bom-2.17.0.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/org/apache/logging/log4j/log4j-bom/2.17.0/log4j-bom-2.17.0.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/logging/log4j/log4j-bom/2.17.0/log4j-bom-2.17.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/logging/log4j/log4j-bom/2.17.0/log4j-bom-2.17.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jacoco/jacoco-maven-plugin/0.8.2/jacoco-maven-plugin-0.8.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jacoco/jacoco-maven-plugin/0.8.2/jacoco-maven-plugin-0.8.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jacoco/org.jacoco.build/0.8.2/org.jacoco.build-0.8.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jacoco/org.jacoco.build/0.8.2/org.jacoco.build-0.8.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jacoco/jacoco-maven-plugin/0.8.2/jacoco-maven-plugin-0.8.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jacoco/jacoco-maven-plugin/0.8.2/jacoco-maven-plugin-0.8.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-clean-plugin/3.2.0/maven-clean-plugin-3.2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-clean-plugin/3.2.0/maven-clean-plugin-3.2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-plugins/35/maven-plugins-35.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-plugins/35/maven-plugins-35.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-parent/35/maven-parent-35.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-parent/35/maven-parent-35.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/apache/25/apache-25.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/apache/25/apache-25.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-clean-plugin/3.2.0/maven-clean-plugin-3.2.0.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-clean-plugin/3.2.0/maven-clean-plugin-3.2.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-jar-plugin/3.2.2/maven-jar-plugin-3.2.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-jar-plugin/3.2.2/maven-jar-plugin-3.2.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-jar-plugin/3.2.2/maven-jar-plugin-3.2.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-jar-plugin/3.2.2/maven-jar-plugin-3.2.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-compiler-plugin/3.10.1/maven-compiler-plugin-3.10.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-compiler-plugin/3.10.1/maven-compiler-plugin-3.10.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-compiler-plugin/3.10.1/maven-compiler-plugin-3.10.1.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-compiler-plugin/3.10.1/maven-compiler-plugin-3.10.1.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-surefire-plugin/2.22.2/maven-surefire-plugin-2.22.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-surefire-plugin/2.22.2/maven-surefire-plugin-2.22.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/surefire/surefire/2.22.2/surefire-2.22.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/surefire/surefire/2.22.2/surefire-2.22.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-surefire-plugin/2.22.2/maven-surefire-plugin-2.22.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-surefire-plugin/2.22.2/maven-surefire-plugin-2.22.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-install-plugin/2.5.2/maven-install-plugin-2.5.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-install-plugin/2.5.2/maven-install-plugin-2.5.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-plugins/25/maven-plugins-25.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-plugins/25/maven-plugins-25.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-install-plugin/2.5.2/maven-install-plugin-2.5.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-install-plugin/2.5.2/maven-install-plugin-2.5.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-deploy-plugin/2.8.2/maven-deploy-plugin-2.8.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-deploy-plugin/2.8.2/maven-deploy-plugin-2.8.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-deploy-plugin/2.8.2/maven-deploy-plugin-2.8.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-deploy-plugin/2.8.2/maven-deploy-plugin-2.8.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Progress (1): 4.1/7.4 kB
Progress (1): 7.4 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom (7.4 kB at 20 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Progress (1): 4.1/35 kB
Progress (1): 8.2/35 kB
Progress (1): 12/35 kB 
Progress (1): 16/35 kB
Progress (1): 20/35 kB
Progress (1): 25/35 kB
Progress (1): 29/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom (35 kB at 94 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/junit/junit-bom/5.8.1/junit-bom-5.8.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/junit/junit-bom/5.8.1/junit-bom-5.8.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Progress (1): 4.1/68 kB
Progress (1): 8.2/68 kB
Progress (1): 12/68 kB 
Progress (1): 16/68 kB
Progress (1): 20/68 kB
Progress (1): 25/68 kB
Progress (1): 29/68 kB
Progress (1): 33/68 kB
Progress (1): 37/68 kB
Progress (1): 41/68 kB
Progress (1): 45/68 kB
Progress (1): 49/68 kB
Progress (1): 53/68 kB
Progress (1): 57/68 kB
Progress (1): 61/68 kB
Progress (1): 66/68 kB
Progress (1): 68 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar (68 kB at 179 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Progress (1): 4.1/4.7 kB
Progress (1): 4.7 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom (4.7 kB at 15 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/flywaydb/flyway-parent/8.5.13/flyway-parent-8.5.13.pom
Progress (1): 4.1/31 kB
Progress (1): 8.2/31 kB
Progress (1): 12/31 kB 
Progress (1): 16/31 kB
Progress (1): 20/31 kB
Progress (1): 25/31 kB
Progress (1): 29/31 kB
Progress (1): 31 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/flywaydb/flyway-parent/8.5.13/flyway-parent-8.5.13.pom (31 kB at 93 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Progress (1): 4.1/130 kB
Progress (1): 8.2/130 kB
Progress (1): 12/130 kB 
Progress (1): 16/130 kB
Progress (1): 20/130 kB
Progress (1): 25/130 kB
Progress (1): 29/130 kB
Progress (1): 33/130 kB
Progress (1): 37/130 kB
Progress (1): 41/130 kB
Progress (1): 45/130 kB
Progress (1): 49/130 kB
Progress (1): 53/130 kB
Progress (1): 57/130 kB
Progress (1): 61/130 kB
Progress (1): 66/130 kB
Progress (1): 68/130 kB
Progress (1): 72/130 kB
Progress (1): 76/130 kB
Progress (1): 80/130 kB
Progress (1): 84/130 kB
Progress (1): 88/130 kB
Progress (1): 92/130 kB
Progress (1): 96/130 kB
Progress (1): 100/130 kB
Progress (1): 105/130 kB
Progress (1): 109/130 kB
Progress (1): 113/130 kB
Progress (1): 117/130 kB
Progress (1): 121/130 kB
Progress (1): 125/130 kB
Progress (1): 129/130 kB
Progress (1): 130 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar (130 kB at 278 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Progress (1): 4.1/12 kB
Progress (1): 7.3/12 kB
Progress (1): 11/12 kB 
Progress (1): 12 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom (12 kB at 26 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Progress (1): 4.1/12 kB
Progress (1): 8.2/12 kB
Progress (1): 12/12 kB 
Progress (1): 12 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom (12 kB at 38 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/oss/oss-parent/9/oss-parent-9.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/oss/oss-parent/9/oss-parent-9.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Progress (1): 4.1/40 kB
Progress (1): 8.2/40 kB
Progress (1): 12/40 kB 
Progress (1): 16/40 kB
Progress (1): 20/40 kB
Progress (1): 25/40 kB
Progress (1): 29/40 kB
Progress (1): 33/40 kB
Progress (1): 37/40 kB
Progress (1): 40 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar (40 kB at 116 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Progress (1): 3.4/4.7 kB
Progress (1): 4.7 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom (4.7 kB at 16 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Progress (1): 3.4/31 kB
Progress (1): 7.5/31 kB
Progress (1): 12/31 kB 
Progress (1): 16/31 kB
Progress (1): 20/31 kB
Progress (1): 24/31 kB
Progress (1): 28/31 kB
Progress (1): 31 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom (31 kB at 94 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Progress (1): 4.1/35 kB
Progress (1): 8.2/35 kB
Progress (1): 12/35 kB 
Progress (1): 16/35 kB
Progress (1): 20/35 kB
Progress (1): 25/35 kB
Progress (1): 29/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar (35 kB at 60 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Progress (1): 4.1/4.1 kB
Progress (1): 4.1 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom (4.1 kB at 14 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Progress (1): 3.4/28 kB
Progress (1): 7.5/28 kB
Progress (1): 12/28 kB 
Progress (1): 16/28 kB
Progress (1): 20/28 kB
Progress (1): 24/28 kB
Progress (1): 28 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom (28 kB at 98 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Progress (1): 4.1/17 kB
Progress (1): 7.3/17 kB
Progress (1): 11/17 kB 
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar (17 kB at 46 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Progress (1): 4.1/5.9 kB
Progress (1): 5.9 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom (5.9 kB at 20 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Progress (1): 4.1/10 kB
Progress (1): 7.3/10 kB
Progress (1): 10 kB    
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom (10 kB at 29 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Progress (1): 4.1/80 kB
Progress (1): 8.2/80 kB
Progress (1): 12/80 kB 
Progress (1): 16/80 kB
Progress (1): 20/80 kB
Progress (1): 25/80 kB
Progress (1): 29/80 kB
Progress (1): 33/80 kB
Progress (1): 37/80 kB
Progress (1): 41/80 kB
Progress (1): 45/80 kB
Progress (1): 49/80 kB
Progress (1): 53/80 kB
Progress (1): 57/80 kB
Progress (1): 61/80 kB
Progress (1): 66/80 kB
Progress (1): 70/80 kB
Progress (1): 74/80 kB
Progress (1): 78/80 kB
Progress (1): 80 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar (80 kB at 113 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Progress (1): 2.7 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom (2.7 kB at 8.0 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Progress (1): 3.4/108 kB
Progress (1): 7.5/108 kB
Progress (1): 12/108 kB 
Progress (1): 16/108 kB
Progress (1): 20/108 kB
Progress (1): 24/108 kB
Progress (1): 28/108 kB
Progress (1): 32/108 kB
Progress (1): 36/108 kB
Progress (1): 40/108 kB
Progress (1): 44/108 kB
Progress (1): 48/108 kB
Progress (1): 53/108 kB
Progress (1): 57/108 kB
Progress (1): 61/108 kB
Progress (1): 65/108 kB
Progress (1): 69/108 kB
Progress (1): 73/108 kB
Progress (1): 77/108 kB
Progress (1): 81/108 kB
Progress (1): 85/108 kB
Progress (1): 89/108 kB
Progress (1): 94/108 kB
Progress (1): 98/108 kB
Progress (1): 102/108 kB
Progress (1): 106/108 kB
Progress (1): 108 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar (108 kB at 252 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-antrun-plugin/3.0.0/maven-antrun-plugin-3.0.0.pom
Progress (1): 4.1/4.7 kB
Progress (1): 4.7 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-antrun-plugin/3.0.0/maven-antrun-plugin-3.0.0.pom (4.7 kB at 5.7 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-antrun-plugin/3.0.0/maven-antrun-plugin-3.0.0.jar
Progress (1): 4.1/39 kB
Progress (1): 8.2/39 kB
Progress (1): 12/39 kB 
Progress (1): 16/39 kB
Progress (1): 20/39 kB
Progress (1): 25/39 kB
Progress (1): 29/39 kB
Progress (1): 33/39 kB
Progress (1): 37/39 kB
Progress (1): 39 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-antrun-plugin/3.0.0/maven-antrun-plugin-3.0.0.jar (39 kB at 65 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-assembly-plugin/3.3.0/maven-assembly-plugin-3.3.0.pom
Progress (1): 3.4/16 kB
Progress (1): 7.5/16 kB
Progress (1): 12/16 kB 
Progress (1): 16/16 kB
Progress (1): 16 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-assembly-plugin/3.3.0/maven-assembly-plugin-3.3.0.pom (16 kB at 51 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-assembly-plugin/3.3.0/maven-assembly-plugin-3.3.0.jar
Progress (1): 4.1/242 kB
Progress (1): 8.2/242 kB
Progress (1): 12/242 kB 
Progress (1): 16/242 kB
Progress (1): 20/242 kB
Progress (1): 25/242 kB
Progress (1): 29/242 kB
Progress (1): 33/242 kB
Progress (1): 37/242 kB
Progress (1): 41/242 kB
Progress (1): 45/242 kB
Progress (1): 49/242 kB
Progress (1): 53/242 kB
Progress (1): 57/242 kB
Progress (1): 61/242 kB
Progress (1): 66/242 kB
Progress (1): 70/242 kB
Progress (1): 74/242 kB
Progress (1): 78/242 kB
Progress (1): 82/242 kB
Progress (1): 86/242 kB
Progress (1): 90/242 kB
Progress (1): 94/242 kB
Progress (1): 98/242 kB
Progress (1): 102/242 kB
Progress (1): 106/242 kB
Progress (1): 111/242 kB
Progress (1): 115/242 kB
Progress (1): 119/242 kB
Progress (1): 123/242 kB
Progress (1): 127/242 kB
Progress (1): 131/242 kB
Progress (1): 135/242 kB
Progress (1): 139/242 kB
Progress (1): 143/242 kB
Progress (1): 147/242 kB
Progress (1): 152/242 kB
Progress (1): 156/242 kB
Progress (1): 160/242 kB
Progress (1): 164/242 kB
Progress (1): 168/242 kB
Progress (1): 172/242 kB
Progress (1): 176/242 kB
Progress (1): 180/242 kB
Progress (1): 184/242 kB
Progress (1): 188/242 kB
Progress (1): 193/242 kB
Progress (1): 197/242 kB
Progress (1): 201/242 kB
Progress (1): 205/242 kB
Progress (1): 209/242 kB
Progress (1): 213/242 kB
Progress (1): 217/242 kB
Progress (1): 221/242 kB
Progress (1): 225/242 kB
Progress (1): 229/242 kB
Progress (1): 233/242 kB
Progress (1): 238/242 kB
Progress (1): 242/242 kB
Progress (1): 242 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-assembly-plugin/3.3.0/maven-assembly-plugin-3.3.0.jar (242 kB at 334 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-dependency-plugin/3.3.0/maven-dependency-plugin-3.3.0.pom
Progress (1): 3.4/16 kB
Progress (1): 7.5/16 kB
Progress (1): 12/16 kB 
Progress (1): 16/16 kB
Progress (1): 16 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-dependency-plugin/3.3.0/maven-dependency-plugin-3.3.0.pom (16 kB at 46 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-dependency-plugin/3.3.0/maven-dependency-plugin-3.3.0.jar
Progress (1): 4.1/187 kB
Progress (1): 8.2/187 kB
Progress (1): 12/187 kB 
Progress (1): 16/187 kB
Progress (1): 20/187 kB
Progress (1): 25/187 kB
Progress (1): 29/187 kB
Progress (1): 33/187 kB
Progress (1): 37/187 kB
Progress (1): 40/187 kB
Progress (1): 44/187 kB
Progress (1): 48/187 kB
Progress (1): 52/187 kB
Progress (1): 56/187 kB
Progress (1): 60/187 kB
Progress (1): 64/187 kB
Progress (1): 69/187 kB
Progress (1): 73/187 kB
Progress (1): 77/187 kB
Progress (1): 81/187 kB
Progress (1): 85/187 kB
Progress (1): 89/187 kB
Progress (1): 93/187 kB
Progress (1): 97/187 kB
Progress (1): 101/187 kB
Progress (1): 104/187 kB
Progress (1): 108/187 kB
Progress (1): 112/187 kB
Progress (1): 116/187 kB
Progress (1): 121/187 kB
Progress (1): 125/187 kB
Progress (1): 129/187 kB
Progress (1): 133/187 kB
Progress (1): 137/187 kB
Progress (1): 141/187 kB
Progress (1): 145/187 kB
Progress (1): 149/187 kB
Progress (1): 153/187 kB
Progress (1): 157/187 kB
Progress (1): 161/187 kB
Progress (1): 165/187 kB
Progress (1): 170/187 kB
Progress (1): 174/187 kB
Progress (1): 178/187 kB
Progress (1): 182/187 kB
Progress (1): 186/187 kB
Progress (1): 187 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/plugins/maven-dependency-plugin/3.3.0/maven-dependency-plugin-3.3.0.jar (187 kB at 349 kB/s)
[INFO] 
[INFO] --------------------< com.jlr.ecp:saml-sso-project >--------------------
[INFO] Building saml-sso-project 2.7.14
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/3.0.0/thymeleaf-layout-dialect-3.0.0.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/3.0.0/thymeleaf-layout-dialect-3.0.0.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/3.0.0/thymeleaf-layout-dialect-3.0.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/3.0.0/thymeleaf-layout-dialect-3.0.0.pom (0 B at 0 B/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/nz/net/ultraq/extensions/groovy-extensions/1.1.0/groovy-extensions-1.1.0.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/nz/net/ultraq/extensions/groovy-extensions/1.1.0/groovy-extensions-1.1.0.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/nz/net/ultraq/extensions/groovy-extensions/1.1.0/groovy-extensions-1.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/nz/net/ultraq/extensions/groovy-extensions/1.1.0/groovy-extensions-1.1.0.pom (0 B at 0 B/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/nz/net/ultraq/thymeleaf/thymeleaf-expression-processor/3.0.0/thymeleaf-expression-processor-3.0.0.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/nz/net/ultraq/thymeleaf/thymeleaf-expression-processor/3.0.0/thymeleaf-expression-processor-3.0.0.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/nz/net/ultraq/thymeleaf/thymeleaf-expression-processor/3.0.0/thymeleaf-expression-processor-3.0.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/nz/net/ultraq/thymeleaf/thymeleaf-expression-processor/3.0.0/thymeleaf-expression-processor-3.0.0.pom (0 B at 0 B/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/org/ow2/asm/asm/9.3/asm-9.3.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/org/ow2/asm/asm/9.3/asm-9.3.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/ow2/asm/asm/9.3/asm-9.3.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/ow2/asm/asm/9.3/asm-9.3.pom (0 B at 0 B/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/joda-time/joda-time/2.2/joda-time-2.2.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/joda-time/joda-time/2.2/joda-time-2.2.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/joda-time/joda-time/2.2/joda-time-2.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/joda-time/joda-time/2.2/joda-time-2.2.pom (0 B at 0 B/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 788 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml (788 B at 1.1 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-system-feign-client-sdk:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-system-feign-client-sdk:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-system-feign-client-sdk:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/ecp-system-feign-client-sdk-1.0.0-20240808.030004-7.pom
Progress (1): 2.2 kB
                    
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-system-feign-client-sdk/1.0.0-SNAPSHOT/ecp-system-feign-client-sdk-1.0.0-20240808.030004-7.pom (2.2 kB at 4.5 kB/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-system-service/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-system-service/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 606 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-system-service/1.0.0-SNAPSHOT/maven-metadata.xml (606 B at 1.3 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-system-service:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-system-service/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-parent/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-parent/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 616 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-parent/1.0.0-SNAPSHOT/maven-metadata.xml (616 B at 1.3 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-parent:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-parent/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-parent/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-parent/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 598 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-parent/1.0.0-SNAPSHOT/maven-metadata.xml (598 B at 1.2 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-parent:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-parent/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-dependencies/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-dependencies/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 604 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-dependencies/1.0.0-SNAPSHOT/maven-metadata.xml (604 B at 1.4 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-dependencies:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-dependencies/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-common/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-common/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 789 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-common/1.0.0-SNAPSHOT/maven-metadata.xml (789 B at 1.8 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-common:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-common/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-framework-starter-common:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-framework-starter-common:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-common/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-bom/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-bom/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 613 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-bom/1.0.0-SNAPSHOT/maven-metadata.xml (613 B at 1.7 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-bom:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-bom/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-rpc/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-rpc/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 786 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-rpc/1.0.0-SNAPSHOT/maven-metadata.xml (786 B at 1.9 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-rpc:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-rpc/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-framework-starter-rpc:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-framework-starter-rpc:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-rpc/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-biz-data-permission/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-biz-data-permission/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 802 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-biz-data-permission/1.0.0-SNAPSHOT/maven-metadata.xml (802 B at 2.3 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-biz-data-permission:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-biz-data-permission/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-framework-starter-biz-data-permission:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-framework-starter-biz-data-permission:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-biz-data-permission/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-mybatis/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-mybatis/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 790 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-mybatis/1.0.0-SNAPSHOT/maven-metadata.xml (790 B at 1.9 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-mybatis:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-mybatis/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-framework-starter-mybatis:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-framework-starter-mybatis:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-mybatis/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-web/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-web/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 786 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-web/1.0.0-SNAPSHOT/maven-metadata.xml (786 B at 1.6 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-web:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-web/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-framework-starter-web:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-framework-starter-web:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-web/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-kafka/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-kafka/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 788 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-framework-starter-kafka/1.0.0-SNAPSHOT/maven-metadata.xml (788 B at 2.2 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-framework-starter-kafka:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-kafka/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-framework-starter-kafka:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-framework-starter-kafka:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-framework-starter-kafka/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/org/springframework/kafka/spring-kafka/2.8.11/spring-kafka-2.8.11.pom
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/org/springframework/kafka/spring-kafka/2.8.11/spring-kafka-2.8.11.pom
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/springframework/kafka/spring-kafka/2.8.11/spring-kafka-2.8.11.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/springframework/kafka/spring-kafka/2.8.11/spring-kafka-2.8.11.pom (0 B at 0 B/s)
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 789 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml (789 B at 2.3 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-product-feign-client-sdk:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] com.jlr.ecp:ecp-product-feign-client-sdk:1.0.0-SNAPSHOT/maven-metadata.xmlfailed to transfer from https://build.shibboleth.net/nexus/content/repositories/releases/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of Shibboleth has elapsed or updates are forced. Original error: Could not transfer metadata com.jlr.ecp:ecp-product-feign-client-sdk:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/ecp-product-feign-client-sdk-1.0.0-20240712.034634-9.pom
Progress (1): 2.2 kB
                    
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-feign-client-sdk/1.0.0-SNAPSHOT/ecp-product-feign-client-sdk-1.0.0-20240712.034634-9.pom (2.2 kB at 7.1 kB/s)
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-service/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from Shibboleth: https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-product-service/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 607 B
                   
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-service/1.0.0-SNAPSHOT/maven-metadata.xml (607 B at 1.9 kB/s)
[WARNING] Could not transfer metadata com.jlr.ecp:ecp-product-service:1.0.0-SNAPSHOT/maven-metadata.xml from/to Shibboleth (https://build.shibboleth.net/nexus/content/repositories/releases/): transfer failed for https://build.shibboleth.net/nexus/content/repositories/releases/com/jlr/ecp/ecp-product-service/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-service/1.0.0-SNAPSHOT/ecp-product-service-1.0.0-20240614.093706-1.pom
Progress (1): 1.7 kB
                    
Downloaded from jlr-ecp-nexus: https://nexus.jaguarlandrover.cn/repository/ecp-public/com/jlr/ecp/ecp-product-service/1.0.0-SNAPSHOT/ecp-product-service-1.0.0-20240614.093706-1.pom (1.7 kB at 5.2 kB/s)
[INFO] 
[INFO] --- maven-dependency-plugin:3.3.0:tree (default-cli) @ saml-sso-project ---
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-impl/3.1.0/maven-reporting-impl-3.1.0.pom
Progress (1): 4.1/7.2 kB
Progress (1): 7.2 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-impl/3.1.0/maven-reporting-impl-3.1.0.pom (7.2 kB at 24 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-api/3.1.0/maven-reporting-api-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-api/3.1.0/maven-reporting-api-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-sink-api/1.11.1/doxia-sink-api-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-sink-api/1.11.1/doxia-sink-api-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia/1.11.1/doxia-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia/1.11.1/doxia-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-logging-api/1.11.1/doxia-logging-api-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-logging-api/1.11.1/doxia-logging-api-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.1.0/maven-core-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.1.0/maven-core-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven/3.1.0/maven-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven/3.1.0/maven-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/3.1.0/maven-model-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/3.1.0/maven-model-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.1.0/maven-settings-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.1.0/maven-settings-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.1.0/maven-settings-builder-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.1.0/maven-settings-builder-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-interpolation/1.16/plexus-interpolation-1.16.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-interpolation/1.16/plexus-interpolation-1.16.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.1.0/maven-repository-metadata-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.1.0/maven-repository-metadata-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/3.1.0/maven-artifact-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/3.1.0/maven-artifact-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.1.0/maven-plugin-api-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.1.0/maven-plugin-api-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/org.eclipse.sisu.plexus/0.0.0.M5/org.eclipse.sisu.plexus-0.0.0.M5.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/org.eclipse.sisu.plexus/0.0.0.M5/org.eclipse.sisu.plexus-0.0.0.M5.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/sisu-plexus/0.0.0.M5/sisu-plexus-0.0.0.M5.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/sisu-plexus/0.0.0.M5/sisu-plexus-0.0.0.M5.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/guava/guava/10.0.1/guava-10.0.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/guava/guava/10.0.1/guava-10.0.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/guava/guava-parent/10.0.1/guava-parent-10.0.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/guava/guava-parent/10.0.1/guava-parent-10.0.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-guice/3.1.0/sisu-guice-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-guice/3.1.0/sisu-guice-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guice-parent/3.1.0/guice-parent-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guice-parent/3.1.0/guice-parent-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/org.eclipse.sisu.inject/0.0.0.M5/org.eclipse.sisu.inject-0.0.0.M5.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/org.eclipse.sisu.inject/0.0.0.M5/org.eclipse.sisu.inject-0.0.0.M5.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/sisu-inject/0.0.0.M5/sisu-inject-0.0.0.M5.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/sisu-inject/0.0.0.M5/sisu-inject-0.0.0.M5.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.4/plexus-classworlds-2.4.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.4/plexus-classworlds-2.4.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.1.0/maven-model-builder-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.1.0/maven-model-builder-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.1.0/maven-aether-provider-3.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.1.0/maven-aether-provider-3.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-api/0.9.0.M2/aether-api-0.9.0.M2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-api/0.9.0.M2/aether-api-0.9.0.M2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-spi/0.9.0.M2/aether-spi-0.9.0.M2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-spi/0.9.0.M2/aether-spi-0.9.0.M2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-impl/0.9.0.M2/aether-impl-0.9.0.M2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-impl/0.9.0.M2/aether-impl-0.9.0.M2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.4.2/plexus-classworlds-2.4.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.4.2/plexus-classworlds-2.4.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-shared-utils/3.3.3/maven-shared-utils-3.3.3.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-shared-utils/3.3.3/maven-shared-utils-3.3.3.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-container-default/2.1.0/plexus-container-default-2.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-container-default/2.1.0/plexus-container-default-2.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-containers/2.1.0/plexus-containers-2.1.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-containers/2.1.0/plexus-containers-2.1.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.6.0/plexus-classworlds-2.6.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.6.0/plexus-classworlds-2.6.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/xbean/xbean-reflect/3.7/xbean-reflect-3.7.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/xbean/xbean-reflect/3.7/xbean-reflect-3.7.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/xbean/xbean/3.7/xbean-3.7.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/xbean/xbean/3.7/xbean-3.7.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/geronimo/genesis/genesis-java5-flava/2.0/genesis-java5-flava-2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/geronimo/genesis/genesis-java5-flava/2.0/genesis-java5-flava-2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/geronimo/genesis/genesis-default-flava/2.0/genesis-default-flava-2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/geronimo/genesis/genesis-default-flava/2.0/genesis-default-flava-2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/geronimo/genesis/genesis/2.0/genesis-2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/geronimo/genesis/genesis/2.0/genesis-2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-decoration-model/1.11.1/doxia-decoration-model-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-decoration-model/1.11.1/doxia-decoration-model-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-sitetools/1.11.1/doxia-sitetools-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-sitetools/1.11.1/doxia-sitetools-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-core/1.11.1/doxia-core-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-core/1.11.1/doxia-core-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-text/1.3/commons-text-1.3.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-text/1.3/commons-text-1.3.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-parent/45/commons-parent-45.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-parent/45/commons-parent-45.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-client/4.5.13/httpcomponents-client-4.5.13.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-client/4.5.13/httpcomponents-client-4.5.13.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-core/4.4.13/httpcomponents-core-4.4.13.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-core/4.4.13/httpcomponents-core-4.4.13.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-core/4.4.14/httpcomponents-core-4.4.14.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcomponents-core/4.4.14/httpcomponents-core-4.4.14.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-site-renderer/1.11.1/doxia-site-renderer-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-site-renderer/1.11.1/doxia-site-renderer-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-skin-model/1.11.1/doxia-skin-model-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-skin-model/1.11.1/doxia-skin-model-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-module-xhtml/1.11.1/doxia-module-xhtml-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-module-xhtml/1.11.1/doxia-module-xhtml-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-modules/1.11.1/doxia-modules-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-modules/1.11.1/doxia-modules-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-module-xhtml5/1.11.1/doxia-module-xhtml5-1.11.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-module-xhtml5/1.11.1/doxia-module-xhtml5-1.11.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-i18n/1.0-beta-10/plexus-i18n-1.0-beta-10.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-i18n/1.0-beta-10/plexus-i18n-1.0-beta-10.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-velocity/1.2/plexus-velocity-1.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-velocity/1.2/plexus-velocity-1.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity/1.7/velocity-1.7.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity/1.7/velocity-1.7.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-lang/commons-lang/2.4/commons-lang-2.4.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-lang/commons-lang/2.4/commons-lang-2.4.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity-tools/2.0/velocity-tools-2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity-tools/2.0/velocity-tools-2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-digester/commons-digester/1.8/commons-digester-1.8.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-digester/commons-digester/1.8/commons-digester-1.8.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-logging/commons-logging/1.1/commons-logging-1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-logging/commons-logging/1.1/commons-logging-1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/logkit/logkit/1.0.1/logkit-1.0.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/logkit/logkit/1.0.1/logkit-1.0.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/avalon-framework/avalon-framework/4.1.3/avalon-framework-4.1.3.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/avalon-framework/avalon-framework/4.1.3/avalon-framework-4.1.3.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-chain/commons-chain/1.1/commons-chain-1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-chain/commons-chain/1.1/commons-chain-1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/dom4j/dom4j/1.1/dom4j-1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/dom4j/dom4j/1.1/dom4j-1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity/1.6.2/velocity-1.6.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity/1.6.2/velocity-1.6.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-io/commons-io/2.11.0/commons-io-2.11.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-io/commons-io/2.11.0/commons-io-2.11.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-archiver/4.2.2/plexus-archiver-4.2.2.pom
Progress (1): 4.1/4.4 kB
Progress (1): 4.4 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-archiver/4.2.2/plexus-archiver-4.2.2.pom (4.4 kB at 10 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus/6.1/plexus-6.1.pom
Progress (1): 3.4/24 kB
Progress (1): 7.5/24 kB
Progress (1): 12/24 kB 
Progress (1): 15/24 kB
Progress (1): 19/24 kB
Progress (1): 22/24 kB
Progress (1): 24 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus/6.1/plexus-6.1.pom (24 kB at 71 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-io/3.2.0/plexus-io-3.2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-io/3.2.0/plexus-io-3.2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-compress/1.20/commons-compress-1.20.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-compress/1.20/commons-compress-1.20.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/iq80/snappy/snappy/0.4/snappy-0.4.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/iq80/snappy/snappy/0.4/snappy-0.4.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/tukaani/xz/1.8/xz-1.8.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/tukaani/xz/1.8/xz-1.8.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-utils/3.4.1/plexus-utils-3.4.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-utils/3.4.1/plexus-utils-3.4.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus/8/plexus-8.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus/8/plexus-8.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-analyzer/1.12.0/maven-dependency-analyzer-1.12.0.pom
Progress (1): 4.1/7.2 kB
Progress (1): 7.2 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-analyzer/1.12.0/maven-dependency-analyzer-1.12.0.pom (7.2 kB at 23 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/ow2/asm/asm/9.2/asm-9.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/ow2/asm/asm/9.2/asm-9.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-tree/3.1.0/maven-dependency-tree-3.1.0.pom
Progress (1): 4.1/6.9 kB
Progress (1): 6.9 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-tree/3.1.0/maven-dependency-tree-3.1.0.pom (6.9 kB at 24 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.0.5/maven-core-3.0.5.pom
Progress (1): 3.4/5.5 kB
Progress (1): 5.5 kB    
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.0.5/maven-core-3.0.5.pom (5.5 kB at 8.8 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven/3.0.5/maven-3.0.5.pom
Progress (1): 4.1/22 kB
Progress (1): 8.2/22 kB
Progress (1): 12/22 kB 
Progress (1): 16/22 kB
Progress (1): 20/22 kB
Progress (1): 22 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven/3.0.5/maven-3.0.5.pom (22 kB at 62 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/3.0.5/maven-model-3.0.5.pom
Progress (1): 3.8 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/3.0.5/maven-model-3.0.5.pom (3.8 kB at 12 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.0.5/maven-settings-3.0.5.pom
Progress (1): 1.8 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.0.5/maven-settings-3.0.5.pom (1.8 kB at 6.4 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.0.5/maven-settings-builder-3.0.5.pom
Progress (1): 2.3 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.0.5/maven-settings-builder-3.0.5.pom (2.3 kB at 8.3 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.0.5/maven-repository-metadata-3.0.5.pom
Progress (1): 1.9 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.0.5/maven-repository-metadata-3.0.5.pom (1.9 kB at 5.5 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/3.0.5/maven-artifact-3.0.5.pom
Progress (1): 1.6 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/3.0.5/maven-artifact-3.0.5.pom (1.6 kB at 4.2 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.0.5/maven-plugin-api-3.0.5.pom
Progress (1): 2.7 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.0.5/maven-plugin-api-3.0.5.pom (2.7 kB at 9.4 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-inject-plexus/2.3.0/sisu-inject-plexus-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-inject-plexus/2.3.0/sisu-inject-plexus-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guice-plexus/2.3.0/guice-plexus-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guice-plexus/2.3.0/guice-plexus-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guice-bean/2.3.0/guice-bean-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guice-bean/2.3.0/guice-bean-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/containers/2.3.0/containers-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/containers/2.3.0/containers-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-inject/2.3.0/sisu-inject-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-inject/2.3.0/sisu-inject-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-parent/2.3.0/sisu-parent-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-parent/2.3.0/sisu-parent-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-inject-bean/2.3.0/sisu-inject-bean-2.3.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-inject-bean/2.3.0/sisu-inject-bean-2.3.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-guava/0.9.9/sisu-guava-0.9.9.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-guava/0.9.9/sisu-guava-0.9.9.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guava-parent/0.9.9/guava-parent-0.9.9.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/inject/guava-parent/0.9.9/guava-parent-0.9.9.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.0.5/maven-model-builder-3.0.5.pom
Progress (1): 2.5 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.0.5/maven-model-builder-3.0.5.pom (2.5 kB at 8.5 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.0.5/maven-aether-provider-3.0.5.pom
Progress (1): 2.8 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.0.5/maven-aether-provider-3.0.5.pom (2.8 kB at 9.4 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-api/1.13.1/aether-api-1.13.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-api/1.13.1/aether-api-1.13.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether/1.13.1/aether-1.13.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether/1.13.1/aether-1.13.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-spi/1.13.1/aether-spi-1.13.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-spi/1.13.1/aether-spi-1.13.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-util/1.13.1/aether-util-1.13.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-util/1.13.1/aether-util-1.13.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-impl/1.13.1/aether-impl-1.13.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/aether/aether-impl/1.13.1/aether-impl-1.13.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-util/1.1.0/aether-util-1.1.0.pom
Progress (1): 2.1 kB
                    
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-util/1.1.0/aether-util-1.1.0.pom (2.1 kB at 6.9 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether/1.1.0/aether-1.1.0.pom
Progress (1): 4.1/26 kB
Progress (1): 7.3/26 kB
Progress (1): 11/26 kB 
Progress (1): 16/26 kB
Progress (1): 20/26 kB
Progress (1): 24/26 kB
Progress (1): 26 kB   
                   
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether/1.1.0/aether-1.1.0.pom (26 kB at 68 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-common-artifact-filters/3.2.0/maven-common-artifact-filters-3.2.0.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-common-artifact-filters/3.2.0/maven-common-artifact-filters-3.2.0.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/3.1.1/maven-artifact-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/3.1.1/maven-artifact-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven/3.1.1/maven-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven/3.1.1/maven-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/3.1.1/maven-model-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/3.1.1/maven-model-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.1.1/maven-core-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.1.1/maven-core-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.1.1/maven-settings-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.1.1/maven-settings-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.1.1/maven-settings-builder-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.1.1/maven-settings-builder-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-interpolation/1.19/plexus-interpolation-1.19.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-interpolation/1.19/plexus-interpolation-1.19.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.1.1/maven-repository-metadata-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.1.1/maven-repository-metadata-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.1.1/maven-plugin-api-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.1.1/maven-plugin-api-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.1.1/maven-model-builder-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.1.1/maven-model-builder-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.1.1/maven-aether-provider-3.1.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.1.1/maven-aether-provider-3.1.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-artifact-transfer/0.13.1/maven-artifact-transfer-0.13.1.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-artifact-transfer/0.13.1/maven-artifact-transfer-0.13.1.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-collections4/4.2/commons-collections4-4.2.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-collections4/4.2/commons-collections4-4.2.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.pom
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.pom (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.1.0/maven-core-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.1.0/maven-settings-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings-builder/3.1.0/maven-settings-builder-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-api/3.1.0/maven-reporting-api-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-impl/3.1.0/maven-reporting-impl-3.1.0.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-core/3.1.0/maven-core-3.1.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-repository-metadata/3.1.0/maven-repository-metadata-3.1.0.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-settings/3.1.0/maven-settings-3.1.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model-builder/3.1.0/maven-model-builder-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.1.0/maven-aether-provider-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-spi/0.9.0.M2/aether-spi-0.9.0.M2.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-impl/0.9.0.M2/aether-impl-0.9.0.M2.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-interpolation/1.16/plexus-interpolation-1.16.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-aether-provider/3.1.0/maven-aether-provider-3.1.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.4.2/plexus-classworlds-2.4.2.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-plugin-api/3.1.0/maven-plugin-api-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-sink-api/1.11.1/doxia-sink-api-1.11.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-logging-api/1.11.1/doxia-logging-api-1.11.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-decoration-model/1.11.1/doxia-decoration-model-1.11.1.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-classworlds/2.4.2/plexus-classworlds-2.4.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-core/1.11.1/doxia-core-1.11.1.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-logging-api/1.11.1/doxia-logging-api-1.11.1.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-container-default/2.1.0/plexus-container-default-2.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/xbean/xbean-reflect/3.7/xbean-reflect-3.7.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/collections/google-collections/1.0/google-collections-1.0.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-container-default/2.1.0/plexus-container-default-2.1.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-text/1.3/commons-text-1.3.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/xbean/xbean-reflect/3.7/xbean-reflect-3.7.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-site-renderer/1.11.1/doxia-site-renderer-1.11.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-skin-model/1.11.1/doxia-skin-model-1.11.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-module-xhtml/1.11.1/doxia-module-xhtml-1.11.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-module-xhtml5/1.11.1/doxia-module-xhtml5-1.11.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-i18n/1.0-beta-10/plexus-i18n-1.0-beta-10.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-site-renderer/1.11.1/doxia-site-renderer-1.11.1.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-velocity/1.2/plexus-velocity-1.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/doxia/doxia-skin-model/1.11.1/doxia-skin-model-1.11.1.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity/1.7/velocity-1.7.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-lang/commons-lang/2.4/commons-lang-2.4.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity-tools/2.0/velocity-tools-2.0.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/velocity/velocity/1.7/velocity-1.7.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-digester/commons-digester/1.8/commons-digester-1.8.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-chain/commons-chain/1.1/commons-chain-1.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/dom4j/dom4j/1.1/dom4j-1.1.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-lang/commons-lang/2.4/commons-lang-2.4.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-compress/1.20/commons-compress-1.20.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-archiver/4.2.2/plexus-archiver-4.2.2.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/iq80/snappy/snappy/0.4/snappy-0.4.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/tukaani/xz/1.8/xz-1.8.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-utils/3.4.1/plexus-utils-3.4.1.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-compress/1.20/commons-compress-1.20.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-io/3.2.0/plexus-io-3.2.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-analyzer/1.12.0/maven-dependency-analyzer-1.12.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/ow2/asm/asm/9.2/asm-9.2.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-component-annotations/2.1.1/plexus-component-annotations-2.1.1.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/codehaus/plexus/plexus-io/3.2.0/plexus-io-3.2.0.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-model/2.0.5/maven-model-2.0.5.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/maven-artifact/2.0.5/maven-artifact-2.0.5.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/ow2/asm/asm/9.2/asm-9.2.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-tree/3.1.0/maven-dependency-tree-3.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-util/1.1.0/aether-util-1.1.0.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-common-artifact-filters/3.2.0/maven-common-artifact-filters-3.2.0.jar
Progress (1): 4.1/103 kB
Progress (1): 7.3/103 kB
Progress (1): 11/103 kB 
Progress (1): 16/103 kB
Progress (1): 20/103 kB
Progress (1): 24/103 kB
Progress (1): 28/103 kB
Progress (1): 32/103 kB
Progress (1): 36/103 kB
Progress (1): 40/103 kB
Progress (1): 44/103 kB
Progress (1): 48/103 kB
Progress (1): 52/103 kB
Progress (1): 56/103 kB
Progress (1): 60/103 kB
                       
Progress (1): 65/103 kB
                       
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-api/0.9.0.M2/aether-api-0.9.0.M2.jar
Progress (1): 69/103 kB
Progress (1): 73/103 kB
Progress (1): 77/103 kB
Progress (1): 81/103 kB
Progress (1): 85/103 kB
Progress (1): 89/103 kB
Progress (1): 93/103 kB
Progress (1): 97/103 kB
Progress (1): 101/103 kB
Progress (1): 103 kB    
                    
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/org.eclipse.sisu.plexus/0.0.0.M5/org.eclipse.sisu.plexus-0.0.0.M5.jar
Progress (2): 103 kB | 4.1/150 kB
Progress (2): 103 kB | 8.2/150 kB
Progress (2): 103 kB | 12/150 kB 
Progress (2): 103 kB | 16/150 kB
Progress (2): 103 kB | 20/150 kB
Progress (2): 103 kB | 25/150 kB
Progress (2): 103 kB | 29/150 kB
Progress (2): 103 kB | 33/150 kB
Progress (2): 103 kB | 37/150 kB
Progress (2): 103 kB | 41/150 kB
Progress (2): 103 kB | 45/150 kB
Progress (2): 103 kB | 49/150 kB
Progress (2): 103 kB | 53/150 kB
Progress (2): 103 kB | 57/150 kB
Progress (2): 103 kB | 61/150 kB
Progress (2): 103 kB | 66/150 kB
Progress (2): 103 kB | 69/150 kB
Progress (2): 103 kB | 73/150 kB
Progress (2): 103 kB | 77/150 kB
Progress (2): 103 kB | 81/150 kB
Progress (2): 103 kB | 85/150 kB
Progress (2): 103 kB | 90/150 kB
Progress (2): 103 kB | 94/150 kB
Progress (2): 103 kB | 98/150 kB
Progress (2): 103 kB | 102/150 kB
Progress (2): 103 kB | 106/150 kB
Progress (2): 103 kB | 110/150 kB
Progress (2): 103 kB | 114/150 kB
Progress (2): 103 kB | 118/150 kB
Progress (2): 103 kB | 122/150 kB
Progress (2): 103 kB | 126/150 kB
Progress (2): 103 kB | 131/150 kB
Progress (2): 103 kB | 135/150 kB
Progress (2): 103 kB | 139/150 kB
Progress (2): 103 kB | 143/150 kB
Progress (2): 103 kB | 147/150 kB
Progress (2): 103 kB | 150 kB    
                             
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/guava/guava/10.0.1/guava-10.0.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/sonatype/sisu/sisu-guice/3.1.0/sisu-guice-3.1.0-no_aop.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-dependency-tree/3.1.0/maven-dependency-tree-3.1.0.jar (103 kB at 35 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/sisu/org.eclipse.sisu.inject/0.0.0.M5/org.eclipse.sisu.inject-0.0.0.M5.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar (0 B at 0 B/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/shared/maven-artifact-transfer/0.13.1/maven-artifact-transfer-0.13.1.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-collections4/4.2/commons-collections4-4.2.jar
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/eclipse/aether/aether-util/1.1.0/aether-util-1.1.0.jar (150 kB at 47 kB/s)
Downloading from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar
Downloaded from alimaven: http://maven.aliyun.com/nexus/content/repositories/central/org/apache/commons/commons-collections4/4.2/commons-collections4-4.2.jar (0 B at 0 B/s)
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:35 min
[INFO] Finished at: 2025-06-23T14:36:29+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-dependency-plugin:3.3.0:tree (default-cli) on project saml-sso-project: Execution default-cli of goal org.apache.maven.plugins:maven-dependency-plugin:3.3.0:tree failed: Plugin org.apache.maven.plugins:maven-dependency-plugin:3.3.0 or one of its dependencies could not be resolved: The following artifacts could not be resolved: org.apache.maven.reporting:maven-reporting-impl:jar:3.1.0, org.apache.maven.reporting:maven-reporting-api:jar:3.1.0, org.apache.maven:maven-settings-builder:jar:3.1.0, org.apache.maven:maven-repository-metadata:jar:3.1.0, org.apache.maven:maven-model-builder:jar:3.1.0, org.eclipse.aether:aether-spi:jar:0.9.0.M2, org.eclipse.aether:aether-impl:jar:0.9.0.M2, org.codehaus.plexus:plexus-interpolation:jar:1.16, org.apache.maven:maven-plugin-api:jar:3.1.0, org.apache.maven.doxia:doxia-sink-api:jar:1.11.1, org.apache.maven.doxia:doxia-decoration-model:jar:1.11.1, org.apache.maven.doxia:doxia-core:jar:1.11.1, com.google.collections:google-collections:jar:1.0, org.apache.commons:commons-text:jar:1.3, org.apache.httpcomponents:httpclient:jar:4.5.13, org.apache.httpcomponents:httpcore:jar:4.4.14, org.apache.maven.doxia:doxia-module-xhtml:jar:1.11.1, org.apache.maven.doxia:doxia-module-xhtml5:jar:1.11.1, org.codehaus.plexus:plexus-i18n:jar:1.0-beta-10, org.codehaus.plexus:plexus-velocity:jar:1.2, org.apache.velocity:velocity-tools:jar:2.0, commons-digester:commons-digester:jar:1.8, commons-chain:commons-chain:jar:1.1, dom4j:dom4j:jar:1.1, org.codehaus.plexus:plexus-archiver:jar:4.2.2, org.iq80.snappy:snappy:jar:0.4, org.tukaani:xz:jar:1.8, org.codehaus.plexus:plexus-utils:jar:3.4.1, org.apache.maven.shared:maven-dependency-analyzer:jar:1.12.0, org.codehaus.plexus:plexus-component-annotations:jar:2.1.1, org.apache.maven:maven-model:jar:2.0.5, org.apache.maven:maven-artifact:jar:2.0.5, org.apache.maven.shared:maven-common-artifact-filters:jar:3.2.0, org.eclipse.aether:aether-api:jar:0.9.0.M2, org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.0.0.M5, com.google.guava:guava:jar:10.0.1, org.sonatype.sisu:sisu-guice:jar:no_aop:3.1.0, org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.0.0.M5, org.apache.maven.shared:maven-artifact-transfer:jar:0.13.1, commons-beanutils:commons-beanutils:jar:1.9.4, commons-collections:commons-collections:jar:3.2.2: Could not transfer artifact org.apache.maven.reporting:maven-reporting-impl:jar:3.1.0 from/to alimaven (http://maven.aliyun.com/nexus/content/repositories/central/): transfer failed for http://maven.aliyun.com/nexus/content/repositories/central/org/apache/maven/reporting/maven-reporting-impl/3.1.0/maven-reporting-impl-3.1.0.jar: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/PluginResolutionException
