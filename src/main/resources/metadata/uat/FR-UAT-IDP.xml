<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<EntityDescriptor entityID="https://int.enterprise.jaguarlandrover.cn/auth/saml2/enterprise/jlrc-iam2-idp"
                  xmlns="urn:oasis:names:tc:SAML:2.0:metadata" xmlns:query="urn:oasis:names:tc:SAML:metadata:ext:query"
                  xmlns:mdattr="urn:oasis:names:tc:SAML:metadata:attribute"
                  xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion" xmlns:xenc="http://www.w3.org/2001/04/xmlenc#"
                  xmlns:xenc11="http://www.w3.org/2009/xmlenc11#"
                  xmlns:alg="urn:oasis:names:tc:SAML:metadata:algsupport"
                  xmlns:x509qry="urn:oasis:names:tc:SAML:metadata:X509:query"
                  xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
    <IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <KeyDescriptor use="signing">
            <ds:KeyInfo>
                <ds:X509Data>
                    <ds:X509Certificate>
                        MIIDVTCCAj2gAwIBAgIUVsygnH7V3w+BIw9rv64yjyFuXp8wDQYJKoZIhvcNAQELBQAwOjEZMBcG
                        A1UEAwwQc2VydmljZWxheWVycy5pbzELMAkGA1UEBhMCREUxEDAOBgNVBAcMB0dlcm1hbnkwHhcN
                        MjIwMzMwMjIxMDIzWhcNMjUwMzI5MjIxMDIzWjA6MRkwFwYDVQQDDBBzZXJ2aWNlbGF5ZXJzLmlv
                        MQswCQYDVQQGEwJERTEQMA4GA1UEBwwHR2VybWFueTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCC
                        AQoCggEBAMggtlVHRgLlSXCNUykVvYzEPfJGeSwy/NznjLZUW862bMUuxnARDEL6XNk63sIa/NF1
                        4MMJeFmFLtQZ5YnsJ2OJvRc1s5lwDq6SDuARDhHc7PVkGtU8eThuI97kW1Y4DGLC7MFPrqbgw7kb
                        +/DJso5cdmUm48/TFYUevqDPu0F7FPhxcBxn7FyF/0b6Nxqot8BmjzoXNJRQ/CFYHj+8X7lv/HVh
                        K9eC3ZbdNCvG9CiVePzGuGiKmJ3Z/wq32kGu3NPme1rGgfxxq8DqLxYRxmqKYgEi26RlGEFYS50Y
                        usmsJOxqOMTBo1JlgC7HRyx4/sEVIzoH6Wb9rRa8sLXb6fkCAwEAAaNTMFEwHQYDVR0OBBYEFMHx
                        cgepQdD/VOKF0xSwt3Uel0pIMB8GA1UdIwQYMBaAFMHxcgepQdD/VOKF0xSwt3Uel0pIMA8GA1Ud
                        EwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBALMLhFVeQw5y7y4rhw5dUtI0c6mrOGx3eps8
                        q1e57ke0U2DGqI3VjC+0NUlovBP4VEXgiCgtX0u2Ce2rIGRqIpJI26d1zKltfhaGTEAgMKpkGpDP
                        Aq1ftIfBO7X37DqvcdONwAmAiTeCucM2CiW/RzpB4ySmQLdcC4yM+oWxUB4YCmr9t4xol9bm01wj
                        dcAv7m+YbJMpunLOMirF4agP38Fm4Bovgl6ToWg3xH5nwmwds3Uza74qGPneQkTK57WRs1LHOOq8
                        QEehEk0g8B9vwOPftcAUcMdtl9csKU1NcmRUJkHEpX2SU9qcx688pUIpwXRgZISv5l0kaElOnHay
                        2eE=
                    </ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
        </KeyDescriptor>
        <KeyDescriptor use="encryption">
            <ds:KeyInfo>
                <ds:X509Data>
                    <ds:X509Certificate>
                        MIIDVTCCAj2gAwIBAgIUVsygnH7V3w+BIw9rv64yjyFuXp8wDQYJKoZIhvcNAQELBQAwOjEZMBcG
                        A1UEAwwQc2VydmljZWxheWVycy5pbzELMAkGA1UEBhMCREUxEDAOBgNVBAcMB0dlcm1hbnkwHhcN
                        MjIwMzMwMjIxMDIzWhcNMjUwMzI5MjIxMDIzWjA6MRkwFwYDVQQDDBBzZXJ2aWNlbGF5ZXJzLmlv
                        MQswCQYDVQQGEwJERTEQMA4GA1UEBwwHR2VybWFueTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCC
                        AQoCggEBAMggtlVHRgLlSXCNUykVvYzEPfJGeSwy/NznjLZUW862bMUuxnARDEL6XNk63sIa/NF1
                        4MMJeFmFLtQZ5YnsJ2OJvRc1s5lwDq6SDuARDhHc7PVkGtU8eThuI97kW1Y4DGLC7MFPrqbgw7kb
                        +/DJso5cdmUm48/TFYUevqDPu0F7FPhxcBxn7FyF/0b6Nxqot8BmjzoXNJRQ/CFYHj+8X7lv/HVh
                        K9eC3ZbdNCvG9CiVePzGuGiKmJ3Z/wq32kGu3NPme1rGgfxxq8DqLxYRxmqKYgEi26RlGEFYS50Y
                        usmsJOxqOMTBo1JlgC7HRyx4/sEVIzoH6Wb9rRa8sLXb6fkCAwEAAaNTMFEwHQYDVR0OBBYEFMHx
                        cgepQdD/VOKF0xSwt3Uel0pIMB8GA1UdIwQYMBaAFMHxcgepQdD/VOKF0xSwt3Uel0pIMA8GA1Ud
                        EwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBALMLhFVeQw5y7y4rhw5dUtI0c6mrOGx3eps8
                        q1e57ke0U2DGqI3VjC+0NUlovBP4VEXgiCgtX0u2Ce2rIGRqIpJI26d1zKltfhaGTEAgMKpkGpDP
                        Aq1ftIfBO7X37DqvcdONwAmAiTeCucM2CiW/RzpB4ySmQLdcC4yM+oWxUB4YCmr9t4xol9bm01wj
                        dcAv7m+YbJMpunLOMirF4agP38Fm4Bovgl6ToWg3xH5nwmwds3Uza74qGPneQkTK57WRs1LHOOq8
                        QEehEk0g8B9vwOPftcAUcMdtl9csKU1NcmRUJkHEpX2SU9qcx688pUIpwXRgZISv5l0kaElOnHay
                        2eE=
                    </ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
            <EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#rsa-oaep-mgf1p">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
            </EncryptionMethod>
            <EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#aes128-cbc">
                <xenc:KeySize>128</xenc:KeySize>
            </EncryptionMethod>
        </KeyDescriptor>
        <ArtifactResolutionService index="0" Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                                   Location="https://int.enterprise.jaguarlandrover.cn/auth/ArtifactResolver/metaAlias/enterprise/jlrc-iam2-idp"/>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/IDPSloRedirect/metaAlias/enterprise/jlrc-iam2-idp"
                             ResponseLocation="https://int.enterprise.jaguarlandrover.cn/auth/IDPSloRedirect/metaAlias/enterprise/jlrc-iam2-idp"/>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/IDPSloPOST/metaAlias/enterprise/jlrc-iam2-idp"
                             ResponseLocation="https://int.enterprise.jaguarlandrover.cn/auth/IDPSloPOST/metaAlias/enterprise/jlrc-iam2-idp"/>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/IDPSloSoap/metaAlias/enterprise/jlrc-iam2-idp"/>
        <ManageNameIDService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/IDPMniRedirect/metaAlias/enterprise/jlrc-iam2-idp"
                             ResponseLocation="https://int.enterprise.jaguarlandrover.cn/auth/IDPMniRedirect/metaAlias/enterprise/jlrc-iam2-idp"/>
        <ManageNameIDService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/IDPMniPOST/metaAlias/enterprise/jlrc-iam2-idp"
                             ResponseLocation="https://int.enterprise.jaguarlandrover.cn/auth/IDPMniPOST/metaAlias/enterprise/jlrc-iam2-idp"/>
        <ManageNameIDService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/IDPMniSoap/metaAlias/enterprise/jlrc-iam2-idp"/>
        <NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient</NameIDFormat>
        <NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</NameIDFormat>
        <NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</NameIDFormat>
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/SSORedirect/metaAlias/enterprise/jlrc-iam2-idp"/>
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/SSOPOST/metaAlias/enterprise/jlrc-iam2-idp"/>
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                             Location="https://int.enterprise.jaguarlandrover.cn/auth/SSOSoap/metaAlias/enterprise/jlrc-iam2-idp"/>
        <NameIDMappingService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                              Location="https://int.enterprise.jaguarlandrover.cn/auth/NIMSoap/metaAlias/enterprise/jlrc-iam2-idp"/>
        <AssertionIDRequestService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                                   Location="https://int.enterprise.jaguarlandrover.cn/auth/AIDReqSoap/IDPRole/metaAlias/enterprise/jlrc-iam2-idp"/>
        <AssertionIDRequestService Binding="urn:oasis:names:tc:SAML:2.0:bindings:URI"
                                   Location="https://int.enterprise.jaguarlandrover.cn/auth/AIDReqUri/IDPRole/metaAlias/enterprise/jlrc-iam2-idp"/>
    </IDPSSODescriptor>
</EntityDescriptor>

