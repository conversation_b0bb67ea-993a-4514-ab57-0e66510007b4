ecp:
  domain: jaguarlandrover.cn
  home: https://ecp-test.jaguarlandrover.cn/login
  server: ecp-api-test.jaguarlandrover.cn
  apiAddress: https://ecp-api-test.jaguarlandrover.cn

jks:
  path: classpath:/metadata/test/saml/samlKeystore.jks
idp:
  path: classpath:/metadata/test/FR-QA-IDP.xml


spring:
  redis:
    redisson:
      config: |
        masterSlaveServersConfig:
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 3000
          retryAttempts: 3
          retryInterval: 1500
          failedSlaveReconnectionInterval: 3000
          failedSlaveCheckInterval: 60000
          password: ${REDIS_PASSWD:uiMYufcA8urorYSK}
          subscriptionsPerConnection: 5
          clientName: null
          loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 50
          slaveConnectionMinimumIdleSize: 6
          slaveConnectionPoolSize: 24
          masterConnectionMinimumIdleSize: 6
          masterConnectionPoolSize: 12
          readMode: "MASTER"
          subscriptionMode: "MASTER"
          slaveAddresses:
            - "rediss://${REDIS_ADDR_SLAVE}:6379"
          masterAddress: "rediss://${REDIS_ADDR_MASTER}:6379"
          database: 0
        threads: 16
        nettyThreads: 32
        codec: !<org.redisson.codec.Kryo5Codec> {}
        transportMode: "NIO"