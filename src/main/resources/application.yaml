#spring.profiles.active: dev

server:
  port: 8123
  tomcat:
    remoteip:
      protocol-header: X-Forwarded-Proto

logging:
  level:
    org.springframework.security.saml: INFO
    org.opensaml: INFO
    com.vdenotaris.spring.boot.security.saml: INFO
  file: logs/file.log

spring:
  profiles:
      active: ${profiles.active}
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  application:
    name: ecp-saml-service

lc:
  info:
    version: 1.0.0
    base-package: com.jlr.ecp.system
  web:
    admin-ui:
      url:

# ??kafka??
logging.level.org.springframework.kafka: error
logging.level.org.apache.kafka: error
logging.level.org.apache.kafka.common: error
