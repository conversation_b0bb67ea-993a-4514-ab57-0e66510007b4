
package com.vdenotaris.spring.boot.security.saml.web.dto;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import org.springframework.security.core.GrantedAuthority;

public class SAMLAuthenticationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String name;
    private String principal;
    private Collection<? extends GrantedAuthority> authorities;
    private List<String> credentials;
    private boolean authenticated;

    private String nameId;
    private String sessionIndex;
    private String nameIdFormat;
    private String nameIdNameQualifier;
    private String nameIdSpNameQualifier;

    // 新增字段
    private String localEntityID;
    private String remoteEntityID;
    private String relayState;

    // Constructors
    public SAMLAuthenticationDTO() {}

    public SAMLAuthenticationDTO(String name, String principal,
                                 Collection<? extends GrantedAuthority> authorities,
                                 List<String> credentials, boolean authenticated) {
        this.name = name;
        this.principal = principal;
        this.authorities = authorities;
        this.credentials = credentials;
        this.authenticated = authenticated;
    }

    // Get<PERSON> and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    public void setAuthorities(Collection<? extends GrantedAuthority> authorities) {
        this.authorities = authorities;
    }

    public List<String> getCredentials() {
        return credentials;
    }

    public void setCredentials(List<String> credentials) {
        this.credentials = credentials;
    }

    public boolean isAuthenticated() {
        return authenticated;
    }

    public void setAuthenticated(boolean authenticated) {
        this.authenticated = authenticated;
    }

    public String getNameId() {
        return nameId;
    }

    public void setNameId(String nameId) {
        this.nameId = nameId;
    }

    public String getSessionIndex() {
        return sessionIndex;
    }

    public void setSessionIndex(String sessionIndex) {
        this.sessionIndex = sessionIndex;
    }

    public String getNameIdFormat() {
        return nameIdFormat;
    }

    public void setNameIdFormat(String nameIdFormat) {
        this.nameIdFormat = nameIdFormat;
    }

    public String getNameIdNameQualifier() {
        return nameIdNameQualifier;
    }

    public void setNameIdNameQualifier(String nameIdNameQualifier) {
        this.nameIdNameQualifier = nameIdNameQualifier;
    }

    public String getNameIdSpNameQualifier() {
        return nameIdSpNameQualifier;
    }

    public void setNameIdSpNameQualifier(String nameIdSpNameQualifier) {
        this.nameIdSpNameQualifier = nameIdSpNameQualifier;
    }

    public String getLocalEntityID() {
        return localEntityID;
    }

    public void setLocalEntityID(String localEntityID) {
        this.localEntityID = localEntityID;
    }

    public String getRemoteEntityID() {
        return remoteEntityID;
    }

    public void setRemoteEntityID(String remoteEntityID) {
        this.remoteEntityID = remoteEntityID;
    }

    public String getRelayState() {
        return relayState;
    }

    public void setRelayState(String relayState) {
        this.relayState = relayState;
    }

}
