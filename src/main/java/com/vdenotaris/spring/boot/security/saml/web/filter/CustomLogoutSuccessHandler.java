package com.vdenotaris.spring.boot.security.saml.web.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class CustomLogoutSuccessHandler  extends AbstractAuthenticationTargetUrlRequestHandler implements LogoutSuccessHandler {


    private static final Logger LOG = LoggerFactory.getLogger(CustomLogoutSuccessHandler.class);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public CustomLogoutSuccessHandler() {
    }

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        LOG.info("CustomLogoutSuccessHandler Call");

        // 先执行清理工作
        handleDistributedSessionCleanup(request, response);

        // 然后执行重定向
        super.handle(request, response, authentication);

    }


    private void handleDistributedSessionCleanup(HttpServletRequest request, HttpServletResponse response) {
        try {
            String sessionId = getCookieValue(request, "saml_session");
            if (sessionId != null) {
                LOG.info("Cleaning up distributed session: {}", sessionId);

                // 从Redis中删除会话数据
                String sessionKey = "saml:session:" + sessionId;
                String authKey = "saml:auth:" + sessionId;

                stringRedisTemplate.delete(sessionKey);
                stringRedisTemplate.delete(authKey);

                LOG.info("Removed session data from Redis for session: {}", sessionId);

                // 清理客户端cookie
                Cookie cookie = new Cookie("saml_session", "");
                cookie.setMaxAge(0);
                cookie.setPath("/saml");
                cookie.setSecure(true);
                response.addCookie(cookie);
            }
        } catch (Exception e) {
            LOG.error("Error during distributed session cleanup", e);
        }
    }

    private String getCookieValue(HttpServletRequest request, String cookieName) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(cookieName)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}
