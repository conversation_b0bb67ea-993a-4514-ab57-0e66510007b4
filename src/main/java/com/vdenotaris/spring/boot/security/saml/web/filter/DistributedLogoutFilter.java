
// DistributedLogoutFilter.java (已基本正确，只需小调整)
package com.vdenotaris.spring.boot.security.saml.web.filter;

import com.alibaba.fastjson.JSON;
import com.vdenotaris.spring.boot.security.saml.web.core.GrantedAuthorityDeserializer;
import com.vdenotaris.spring.boot.security.saml.web.dto.SAMLAuthenticationDTO;
import org.opensaml.saml2.core.Assertion;
import org.opensaml.saml2.core.AuthnStatement;
import org.opensaml.saml2.core.NameID;
import org.opensaml.saml2.core.impl.AssertionBuilder;
import org.opensaml.saml2.core.impl.AuthnStatementBuilder;
import org.opensaml.saml2.core.impl.NameIDBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.saml.SAMLCredential;
import org.springframework.security.saml.SAMLLogoutFilter;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import com.alibaba.fastjson.parser.ParserConfig;
import org.springframework.security.core.GrantedAuthority;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;

public class DistributedLogoutFilter extends SAMLLogoutFilter {

    private static final Logger LOG = LoggerFactory.getLogger(DistributedLogoutFilter.class);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private AntPathRequestMatcher logoutRequestMatcher = new AntPathRequestMatcher("/saml/logout/**");

    static {
        ParserConfig.getGlobalInstance().putDeserializer(GrantedAuthority.class, new GrantedAuthorityDeserializer());
    }

    public DistributedLogoutFilter(LogoutSuccessHandler logoutSuccessHandler, LogoutHandler[] localHandler, LogoutHandler[] globalHandlers) {
        super(logoutSuccessHandler, localHandler, globalHandlers);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                            FilterChain chain) throws IOException, ServletException {

        LOG.info("DistributedLogoutFilter Call doFilter");
        //类型强制转换
        HttpServletRequest  httpServletRequest = (HttpServletRequest)request;
        // 确保只处理 logout 相关请求
        if (!logoutRequestMatcher.matches(httpServletRequest)) {
            LOG.warn("DistributedLogoutFilter incorrectly called for non-logout URI: {}",
                    httpServletRequest.getRequestURI());
            chain.doFilter(request, response);
            return;
        }
        // 在执行注销逻辑前，先尝试从Redis恢复认证信息
        restoreAuthenticationFromRedis((HttpServletRequest) request);
        // 检查恢复后的状态
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            LOG.info("Authentication restored: name={}, class={}, authorities={}",
                    auth.getName(), auth.getClass(), auth.getAuthorities());
        } else {
            LOG.warn("No authentication found after restore");
        }
        super.doFilter(request, response, chain);
    }


    private void restoreAuthenticationFromRedis(HttpServletRequest request) {
        try {
            Authentication authenticationCurrentContext = SecurityContextHolder.getContext().getAuthentication();
            // 检查SecurityContext是否已经有认证信息
            if (authenticationCurrentContext != null) {
                LOG.info("Authentication already exists in SecurityContext, skipping restore");
                String authenticationString = JSON.toJSONString(authenticationCurrentContext);
                return;
            }

            String sessionId = getCookieValue(request, "saml_session");
            if (sessionId != null) {
                LOG.info("Found saml_session ID: {}", sessionId);
                String redisAuthKey = "saml:auth:" + sessionId;
                // 从Redis中获取认证信息
                String authData = stringRedisTemplate.opsForValue().get(redisAuthKey);
                if (authData != null) {
                    LOG.info("Found authentication data in Redis for session: {}", sessionId);

                    try {
                        // 反序列化认证信息
                        SAMLAuthenticationDTO authDTO = JSON.parseObject(authData, SAMLAuthenticationDTO.class);
                        LOG.info("SAMLAuthenticationDTO parseObject deserializing result:{}", JSON.toJSONString(authDTO));

                        // 改进后的反序列化处理
                        Collection<GrantedAuthority> authorities = new ArrayList<>();
                        if (authDTO.getAuthorities() != null) {
                            for (GrantedAuthority authorityObj : authDTO.getAuthorities()) {
                                    authorities.add(authorityObj);
                            }
                        }
                        // 创建认证对象
                        UsernamePasswordAuthenticationToken authentication = null;

                        // 检查是否包含SAML所需的基本信息
                        if (authDTO.getNameId() != null &&
                                authDTO.getLocalEntityID() != null &&
                                authDTO.getRemoteEntityID() != null) {

                            // 创建NameID对象
                            NameID nameID = new NameIDBuilder().buildObject();
                            nameID.setValue(authDTO.getNameId());
                            if (authDTO.getNameIdFormat() != null) {
                                nameID.setFormat(authDTO.getNameIdFormat());
                            }
                            if (authDTO.getNameIdNameQualifier() != null) {
                                nameID.setNameQualifier(authDTO.getNameIdNameQualifier());
                            }
                            if (authDTO.getNameIdSpNameQualifier() != null) {
                                nameID.setSPNameQualifier(authDTO.getNameIdSpNameQualifier());
                            }

                            // 创建一个最小的Assertion对象以避免null异常
                            Assertion assertion = null;
                            if (authDTO.getSessionIndex() != null) {
                                // 只有在需要sessionIndex时才创建Assertion
                                AssertionBuilder assertionBuilder = new AssertionBuilder();
                                assertion = assertionBuilder.buildObject();

                                // 创建AuthnStatement并设置sessionIndex
                                AuthnStatementBuilder authnStatementBuilder = new AuthnStatementBuilder();
                                AuthnStatement authnStatement = authnStatementBuilder.buildObject();
                                authnStatement.setSessionIndex(authDTO.getSessionIndex());

                                // 将AuthnStatement添加到Assertion
                                assertion.getAuthnStatements().add(authnStatement);
                            }

                            // 创建SAMLCredential对象，使用正确的构造函数
                            SAMLCredential samlCredential = new SAMLCredential(
                                    nameID,
                                    assertion,
                                    authDTO.getRemoteEntityID(),
                                    authDTO.getRelayState(), // relayState
                                    new ArrayList<>(), // attributes (空列表)
                                    authDTO.getLocalEntityID()
                            );

                            // 创建包含SAMLCredential的认证对象
                            authentication = new UsernamePasswordAuthenticationToken(
                                    authDTO.getName(), // 使用name而不是principal
                                    samlCredential,
                                    authorities
                            );
                        } else {
                            // 回退到基本认证对象
                            authentication = new UsernamePasswordAuthenticationToken(
                                    authDTO.getPrincipal(),
                                    null,
                                    authorities
                            );
                        }
                        // 设置到SecurityContext
                        SecurityContextHolder.getContext().setAuthentication(authentication);

                        LOG.info("Successfully restored authentication for user: {}", authDTO.getName());
                    } catch (Exception e) {
                        LOG.error("Error deserializing authentication data from Redis", e);
                    }
                } else {
                    LOG.warn("No authentication data found in Redis for session: {}", sessionId);
                }
            } else {
                LOG.warn("No saml_session cookie found in request");
            }
        } catch (Exception e) {
            LOG.error("Error restoring authentication from Redis", e);
        }
    }

    private String getCookieValue(HttpServletRequest request, String cookieName) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(cookieName)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }


    public static void main(String[] args) {
        // 在您的 DistributedSAMLContextProvider 初始化时注册自定义反序列化器
        ParserConfig.getGlobalInstance().putDeserializer(GrantedAuthority.class, new GrantedAuthorityDeserializer());
        String originalText = "{\"authenticated\":true,\"authorities\":[{\"authority\":\"ROLE_USER\"}],\"credentials\":[\"org.springframework.security.saml.SAMLCredential@7adbffd2\"],\"name\":\"andhuang\",\"principal\":\"org.springframework.security.core.userdetails.User [Username=andhuang, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_USER]]\"}";
        SAMLAuthenticationDTO authDTO = JSON.parseObject(originalText, SAMLAuthenticationDTO.class);
        System.out.println(JSON.toJSONString(authDTO));

        // 改进后的反序列化处理
        Collection<SimpleGrantedAuthority> authorities = new ArrayList<>();
        if (authDTO.getAuthorities() != null) {
            for (Object authorityObj : authDTO.getAuthorities()) {
                if (authorityObj instanceof SimpleGrantedAuthority) {
                    authorities.add((SimpleGrantedAuthority) authorityObj);
                } else {
                    LOG.warn("Unexpected authority type: {}", authorityObj.getClass());
                }
            }
        }
        System.out.println(authorities.size());
    }
}