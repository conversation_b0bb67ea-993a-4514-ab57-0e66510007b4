package com.vdenotaris.spring.boot.security.saml.web.core;


import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.lang.reflect.Type;
import java.util.Map;

public class GrantedAuthorityDeserializer implements ObjectDeserializer {

    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Object obj = parser.parse();

        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;
            Object authority = map.get("authority");
            if (authority != null) {
                return (T) new SimpleGrantedAuthority(authority.toString());
            }
        } else if (obj instanceof String) {
            return (T) new SimpleGrantedAuthority((String) obj);
        }

        return null;
    }


    @Override
    public int getFastMatchToken() {
        return 0;
    }
}

