/*
 * Copyright 2021 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. 
 */

package com.vdenotaris.spring.boot.security.saml.web.controllers;

import java.io.IOException;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.saml.metadata.MetadataManager;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Controller
@RequestMapping("/saml")
public class SSOController {

	// Logger
	private static final Logger LOG = LoggerFactory
			.getLogger(SSOController.class);

	@Autowired
	private MetadataManager metadata;

	@Value("${ecp.home}")
	String ecpHome;

	@Value("${ecp.apiAddress}")
	String apiAddress;

	@RequestMapping(value = "/discovery", method = RequestMethod.GET)
	public String idpSelection(HttpServletRequest request, Model model) {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth == null) {
			LOG.debug("Current authentication instance from security context is null");
		}
		else {
			LOG.debug("Current authentication instance from security context: "
					+ this.getClass().getSimpleName());
		}
		if (auth == null || (auth instanceof AnonymousAuthenticationToken)) {
			Set<String> idps = metadata.getIDPEntityNames();
			for (String idp : idps) {
                LOG.info("Configured Identity Provider for SSO: " + idp);
            }
			model.addAttribute("idps", idps);
			return "pages/discovery";
		} else {
			LOG.warn("The current user is already logged.");
			return "redirect:saml/landing";
		}
	}

	@RequestMapping(value = "/ecpLogin")
	public void ecpLogin(HttpServletResponse response) throws IOException {
		//获取IDP并重定向到登录页
		Set<String> idps = metadata.getIDPEntityNames();
		for (String idp : idps) {
			LOG.info("Configured Identity Provider for SSO: " + idp);
		}
		if (CollectionUtil.isEmpty(idps)){
			throw new RuntimeException("未配置IDP");
		}
		response.sendRedirect(apiAddress + "/saml/login?idp=" + idps.iterator().next());
	}

}
