package com.vdenotaris.spring.boot.security.saml.web.filter;

import com.alibaba.fastjson.JSON;
import com.vdenotaris.spring.boot.security.saml.web.dto.SAMLAuthenticationDTO;
import org.opensaml.saml2.core.Assertion;
import org.opensaml.saml2.core.AuthnStatement;
import org.opensaml.saml2.core.NameID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.saml.SAMLCredential;
import org.springframework.security.saml.SAMLProcessingFilter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

// 1. 自定义SAML响应处理过滤器
public class CustomSAMLProcessingFilter extends SAMLProcessingFilter {
    // Logger
    private static final Logger LOG = LoggerFactory.getLogger(CustomSAMLProcessingFilter.class);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain,Authentication authResult) throws IOException, ServletException {
        LOG.info("saml successfulAuthentication call");
        // 生成唯一会话ID
        String sessionId = UUID.randomUUID().toString();
        User currentUser = (User)authResult.getPrincipal();
        if(currentUser != null){
            LOG.info("saml successfulAuthentication current user is:{}, sessionId is:{}",currentUser.getUsername(),sessionId);
            // 创建结构化的认证信息对象
            SAMLAuthenticationDTO authDTO = new SAMLAuthenticationDTO();
            authDTO.setName(authResult.getName());
            authDTO.setPrincipal(currentUser.toString());
            authDTO.setAuthenticated(authResult.isAuthenticated());

            // 存储权限信息
            Collection<? extends GrantedAuthority> authorities = authResult.getAuthorities();
            if (authorities != null) {
                authDTO.setAuthorities(authorities);
            }

            // 存储SAML特定信息
            if (authResult.getCredentials() instanceof SAMLCredential) {
                SAMLCredential samlCredential = (SAMLCredential) authResult.getCredentials();

                // 存储NameID信息
                NameID nameId = samlCredential.getNameID();
                if (nameId != null) {
                    authDTO.setNameId(nameId.getValue());
                    authDTO.setNameIdFormat(nameId.getFormat());
                    authDTO.setNameIdNameQualifier(nameId.getNameQualifier());
                    authDTO.setNameIdSpNameQualifier(nameId.getSPNameQualifier());
                    LOG.info("Stored NameID: {}, Format: {}", nameId.getValue(), nameId.getFormat());
                }
                // 从Assertion中提取SessionIndex
                Assertion assertion = samlCredential.getAuthenticationAssertion();
                if (assertion != null) {
                    List<AuthnStatement> authnStatements = assertion.getAuthnStatements();
                    if (authnStatements != null && !authnStatements.isEmpty()) {
                        AuthnStatement authnStatement = authnStatements.get(0);
                        if (authnStatement.getSessionIndex() != null) {
                            authDTO.setSessionIndex(authnStatement.getSessionIndex());
                            LOG.info("Stored SessionIndex: {}", authnStatement.getSessionIndex());
                        }
                    }
                }

                // 存储实体ID信息和其他关键信息
                authDTO.setLocalEntityID(samlCredential.getLocalEntityID());
                authDTO.setRemoteEntityID(samlCredential.getRemoteEntityID());
                authDTO.setRelayState(samlCredential.getRelayState());
            }

            // 存储凭据信息（如果需要）
            List<String> credentials = new ArrayList<>();
            if (authResult.getCredentials() != null) {
                credentials.add(authResult.getCredentials().toString());
            }
            authDTO.setCredentials(credentials);
            //对DTO做JSON序列化
            String authDTOStr = JSON.toJSONString(authDTO);
            LOG.info("Store Redis Before authDTOStr:{}", authDTOStr);
            // 将结构化的认证对象存储到Redis
            stringRedisTemplate.opsForValue().set("saml:auth:" + sessionId, authDTOStr, 36030L, TimeUnit.SECONDS);
            // 将认证信息存储到Redis（设置与SAML Session相同的过期时间）
            stringRedisTemplate.opsForValue().set("saml:session:" + sessionId, currentUser.getUsername(), 36030L, TimeUnit.SECONDS);
            // 将会话ID写入客户端Cookie
            Cookie cookie = new Cookie("saml_session", sessionId);
            //保持cookie过期时间与session一致
            cookie.setMaxAge(36030);
            cookie.setPath("/saml");
            cookie.setSecure(true);
            response.addCookie(cookie);
        }else {
            LOG.error("saml successfulAuthentication get authResult.getPrincipal is null");
        }
        // 继续执行父类逻辑
        super.successfulAuthentication(request, response, chain, authResult);
    }
}
