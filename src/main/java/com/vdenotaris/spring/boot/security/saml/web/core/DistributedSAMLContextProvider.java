
// DistributedSAMLContextProvider.java
package com.vdenotaris.spring.boot.security.saml.web.core;

import com.alibaba.fastjson.JSON;
import com.vdenotaris.spring.boot.security.saml.web.dto.SAMLAuthenticationDTO;
import org.opensaml.saml2.metadata.provider.MetadataProviderException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.saml.context.SAMLContextProviderLB;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collection;

public class DistributedSAMLContextProvider extends SAMLContextProviderLB {

    private static final Logger LOG = LoggerFactory.getLogger(DistributedSAMLContextProvider.class);
    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public org.springframework.security.saml.context.SAMLMessageContext getLocalEntity(
            HttpServletRequest request, HttpServletResponse response) throws MetadataProviderException {

        LOG.info("Getting local entity for request: {}", request.getRequestURI());

        // 尝试从Redis恢复认证信息到SecurityContext
        restoreAuthenticationFromRedis(request);

        return super.getLocalEntity(request, response);
    }

    private void restoreAuthenticationFromRedis(HttpServletRequest request) {
        try {
            // 检查SecurityContext是否已经有认证信息
            if (SecurityContextHolder.getContext().getAuthentication() != null) {
                LOG.debug("Authentication already exists in SecurityContext, skipping restore");
                return;
            }

            String sessionId = getCookieValue(request, "saml_session");
            if (sessionId != null) {
                LOG.info("Found saml_session ID: {}", sessionId);
                String redisAuthKey = "saml:auth:" + sessionId;
                // 从Redis中获取认证信息
                String authData = redisTemplate.opsForValue().get(redisAuthKey);
                LOG.info("Auth Data from redis :{}",authData);
                if (authData != null) {
                    LOG.info("Found authentication data in Redis for session: {}", sessionId);

                    try {
                        // 反序列化认证信息
                        SAMLAuthenticationDTO authDTO = JSON.parseObject(authData, SAMLAuthenticationDTO.class);
                        LOG.info("SAMLAuthenticationDTO parseObject deserializing result:{}", authDTO.toString());

                        if (authDTO != null && authDTO.getPrincipal() != null) {
                            // 重建认证对象
                            Collection<GrantedAuthority> authorities = new ArrayList<>();
                            if (authDTO.getAuthorities() != null) {
                                for (GrantedAuthority authority : authDTO.getAuthorities()) {
                                    // 更安全的处理方式
                                    if (authority != null) {
                                        String authorityStr = authority.getAuthority();
                                        if (authorityStr != null && !authorityStr.trim().isEmpty()) {
                                            authorities.add(new SimpleGrantedAuthority(authorityStr));
                                        } else {
                                            LOG.warn("Found authority with null or empty authority string");
                                        }
                                    } else {
                                        LOG.warn("Found null authority in authorities list");
                                    }
                                }
                            }

                            // 创建认证对象
                            UsernamePasswordAuthenticationToken authentication =
                                    new UsernamePasswordAuthenticationToken(
                                            authDTO.getPrincipal(),
                                            null, // 凭据通常不存储
                                            authorities
                                    );
                            authentication.setAuthenticated(authDTO.isAuthenticated());

                            // 设置到SecurityContext
                            SecurityContextHolder.getContext().setAuthentication(authentication);

                            LOG.info("Successfully restored authentication for user: {}", authDTO.getName());
                        }
                    } catch (Exception e) {
                        LOG.error("Error deserializing authentication data from Redis", e);
                    }
                } else {
                    LOG.warn("No authentication data found in Redis for session: {}", sessionId);
                }
            } else {
                LOG.warn("No saml_session cookie found in request");
            }
        } catch (Exception e) {
            LOG.error("Error restoring authentication from Redis", e);
        }
    }

    private String getCookieValue(HttpServletRequest request, String cookieName) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(cookieName)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}