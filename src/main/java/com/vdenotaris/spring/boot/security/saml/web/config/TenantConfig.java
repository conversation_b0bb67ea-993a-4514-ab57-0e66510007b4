package com.vdenotaris.spring.boot.security.saml.web.config;

import com.vdenotaris.spring.boot.security.saml.web.core.TenantContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils.HEADER_TENANT_ID;

@Configuration
public class TenantConfig {

    @Bean
    public TenantRequestInterceptor tenantRequestInterceptor() {

        return new TenantRequestInterceptor();
    }

    public class TenantRequestInterceptor implements RequestInterceptor {


        @Override
        public void apply(RequestTemplate requestTemplate) {

            Long tenantId = TenantContextHolder.getTenantId();
            if (tenantId != null) {
                requestTemplate.header(HEADER_TENANT_ID, String.valueOf(tenantId));
            }
        }

    }
}
