/*
 * Copyright 2021 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.vdenotaris.spring.boot.security.saml.web.controllers;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.system.api.saml.UserLoginForSamlApi;
import com.jlr.ecp.system.api.saml.dto.AuthLoginRespVO;
import com.vdenotaris.spring.boot.security.saml.web.core.TenantContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

@Controller
public class LandingController {

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String AUTHORIZATION_BEARER = "Bearer: ";


    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    UserLoginForSamlApi userLoginForSamlApi;

    @Value("${ecp.domain}")
    String ecpDomain;

    @Value("${ecp.home}")
    String ecpHome;

    @Value("${cookie.maxAge:5}")
    Integer maxAge;

    private static final String LOGIN_FAIL_HEADER = "LOGIN_FAIL";

    private static final String LOGIN_SUCCESS_HEADER = "LOGIN_SUCCESS";

    // Logger
    private static final Logger LOG = LoggerFactory.getLogger(LandingController.class);


    @RequestMapping("/saml/landing")
    public String landingNew(HttpServletRequest request,HttpServletResponse response) {
        try {

            //从saml_session这个cookie里获取sessionid
            String sessionId = getCookieValue(request, "saml_session");
            LOG.info("/saml/landing call, the sessionId from cookie is:{}",sessionId);
            //从redis中获取该sessionid key对应的用户信息
            String userId = stringRedisTemplate.opsForValue().get("saml:session:" + sessionId);
            LOG.info("/saml/landing call, the userId from redis is:{}",userId);
            if(sessionId == null || !StringUtils.hasText(userId)){
                Cookie cookie = getErrorCookie();
                response.addCookie(cookie);
            }else {
                    // 去system换登录token
                    TenantContextHolder.setTenantId(1L);
                    CommonResult<AuthLoginRespVO> loginResult = userLoginForSamlApi.getLoginToken(userId);
                    Cookie cookie;
                    if (loginResult.isSuccess() && StringUtils.hasText(loginResult.getData().getAccessToken())) {
                        // 创建Cookie对象
                        cookie = new Cookie(LOGIN_SUCCESS_HEADER, URLEncoder.encode(JSON.toJSONString(loginResult.getData())));
                    } else {
                        cookie = new Cookie(LOGIN_FAIL_HEADER, URLEncoder.encode(loginResult.getMsg()));
                    }
                    // 设置Cookie的域名
                    cookie.setDomain(ecpDomain);
                    // 设置Cookie的路径
                    cookie.setPath("/");
                    // 设置Cookie的过期时间
                    cookie.setMaxAge(maxAge); // 1小时
                    // 将Cookie添加到HttpServletResponse对象中
                    response.addCookie(cookie);
                    // 返回响应对象
            }
            return "redirect:" + ecpHome;
        } catch (Exception e) {
            LOG.info("/saml/landing catch exception :",e.getMessage());
            // 将Cookie添加到HttpServletResponse对象中
            Cookie cookie = getErrorCookie();
            response.addCookie(cookie);
            return "redirect:" + ecpHome;
        }
    }

    public Cookie getErrorCookie() {
        Cookie cookie = new Cookie(LOGIN_FAIL_HEADER, URLEncoder.encode("SSO登录超时，请重新登录!"));
        // 设置Cookie的域名
        cookie.setDomain(ecpDomain);
        // 设置Cookie的路径
        cookie.setPath("/");
        // 设置Cookie的过期时间
        cookie.setMaxAge(maxAge); // 1小时
        return cookie;
    }


    @RequestMapping("/saml/testCookie")
    public void testCookie(@RequestParam("userName") String userName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 去system换登录token
        TenantContextHolder.setTenantId(1L);
        CommonResult<AuthLoginRespVO> loginResult = userLoginForSamlApi.getLoginToken(userName);
        Cookie cookie;
        if (loginResult.isSuccess() && StringUtils.hasText(loginResult.getData().getAccessToken())) {
            // 创建Cookie对象
            cookie = new Cookie(LOGIN_SUCCESS_HEADER, URLEncoder.encode(JSON.toJSONString(loginResult.getData())));
        } else {
            cookie = new Cookie(LOGIN_FAIL_HEADER, URLEncoder.encode(loginResult.getMsg()));
        }

        // 设置Cookie的域名
        cookie.setDomain(ecpDomain);
        // 设置Cookie的路径
        cookie.setPath("/");
        // 设置Cookie的过期时间
        cookie.setMaxAge(maxAge); // 1小时
        // 将Cookie添加到HttpServletResponse对象中
        response.addCookie(cookie);
        response.sendRedirect(ecpHome);
    }

    public static String getCookieValue(HttpServletRequest request, String cookieName) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(cookieName)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

}
