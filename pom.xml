<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
<!--    <parent>-->
<!--        <groupId>org.springframework.boot</groupId>-->
<!--        <artifactId>spring-boot-starter-parent</artifactId>-->
<!--        <version>2.7.14</version>-->
<!--        <relativePath/> &lt;!&ndash; lookup parent from repository &ndash;&gt;-->
<!--    </parent>-->
    <parent>
        <groupId>com.jlr.ecp</groupId>
        <artifactId>ecp-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.jlr.ecp</groupId>
    <artifactId>saml-sso-project</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>A sample SAML 2.0 Service Provider built on Spring Boot.</description>
    <url>https://github.com/vdenotaris/spring-boot-security-saml-sample</url>

    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!--<start-class>com.vdenotaris.spring.boot.security.saml.web.Application</start-class>-->
        <jackson.version>2.13.0</jackson.version>
		<log4j2.version>2.17.0</log4j2.version>
        <!-- 统一依赖管理 -->
        <spring.boot.version>2.7.14</spring.boot.version>
        <spring.cloud.version>2021.0.5</spring.cloud.version>
    </properties>
    <!-- Inherit defaults from Spring Boot -->
    <!--<parent>-->
		<!--<groupId>org.springframework.boot</groupId>-->
		<!--<artifactId>spring-boot-starter-parent</artifactId>-->
		<!--<version>2.6.1</version>-->
		<!--<relativePath/> &lt;!&ndash; lookup parent from repository &ndash;&gt;-->
    <!--</parent>-->
    <!--<repositories>-->
        <!--<repository>-->
            <!--<id>Shibboleth</id>-->
            <!--<name>Shibboleth</name>-->
            <!--<url>https://build.shibboleth.net/nexus/content/repositories/releases/</url>-->
        <!--</repository>-->
    <!--</repositories>-->
    <dependencies>
        <!-- RPC 远程调用相关 -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.90</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-log4j2</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>nz.net.ultraq.thymeleaf</groupId>
            <artifactId>thymeleaf-layout-dialect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-rpc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>4.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.extensions</groupId>
            <artifactId>spring-security-saml2-core</artifactId>
            <version>1.0.10.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.3.4</version>
        </dependency>

        <!-- starter-actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-system-feign-client-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>



    </dependencies>
    <!--<build>-->
        <!--&lt;!&ndash; 设置构建的 jar 包名 &ndash;&gt;-->
        <!--<finalName>app</finalName>-->
        <!--<plugins>-->
            <!--&lt;!&ndash; 打包 &ndash;&gt;-->
            <!--<plugin>-->
                <!--<groupId>org.springframework.boot</groupId>-->
                <!--<artifactId>spring-boot-maven-plugin</artifactId>-->
                <!--<version>${spring.boot.version}</version> &lt;!&ndash; 如果 spring.boot.version 版本修改，则这里也要跟着修改 &ndash;&gt;-->
                <!--<configuration>-->
                    <!--<fork>true</fork>-->
                <!--</configuration>-->
                <!--<executions>-->
                    <!--<execution>-->
                        <!--<goals>-->
                            <!--<goal>repackage</goal> &lt;!&ndash; 将引入的 jar 打入其中 &ndash;&gt;-->
                        <!--</goals>-->
                    <!--</execution>-->
                <!--</executions>-->
            <!--</plugin>-->
        <!--</plugins>-->
    <!--</build>-->
    <dependencyManagement>
        <dependencies>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <repositories>
        <repository>
            <id>jlr-ecp-nexus</id>
            <name>jlr-ecp-nexus</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>Shibboleth</id>
            <name>Shibboleth</name>
            <url>https://build.shibboleth.net/nexus/content/repositories/releases/</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>jlr-ecp-release</id>
            <name>Releases</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-releases/</url>
        </repository>
        <snapshotRepository>
            <id>jlr-ecp-snapshot</id>
            <name>Snapshot</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>app</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19</version>
            </plugin>

        </plugins>
    </build>
</project>
